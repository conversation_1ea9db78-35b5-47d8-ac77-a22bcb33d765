<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校招 - 杨柯 - 2025.08.12</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .interview-info {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 30px;
        }
        
        .interview-info div {
            font-size: 1.1em;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .content {
            padding: 30px;
        }
        
        /* 导航栏样式 */
        .navigation {
            position: sticky;
            top: 0;
            background: white;
            padding: 15px 20px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            border: 2px solid #4facfe;
            z-index: 1000;
        }
        
        .nav-sections {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
            justify-content: center;
        }
        
        .nav-btn {
            padding: 8px 16px;
            background: #f8f9ff;
            border: 2px solid #e1e8ed;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #666;
            position: relative;
            overflow: hidden;
        }
        
        .nav-btn:hover, .nav-btn.active {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
            transform: translateY(-2px);
        }
        
        .nav-btn::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: #4facfe;
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }
        
        .nav-btn.active::after,
        .nav-btn:hover::after {
            width: 100%;
        }
        
        .controls-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .candidate-info {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .candidate-input {
            padding: 10px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1em;
            min-width: 150px;
            transition: all 0.3s ease;
        }
        
        .candidate-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }
        
        .score-display {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
            padding: 12px 24px;
            background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
            border-radius: 25px;
            min-width: 140px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        
        .progress-container {
            margin-top: 15px;
        }
        
        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.9em;
            color: #666;
        }
        
        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e1e8ed;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 5px;
        }
        
        /* 章节样式 */
        .section {
            margin-bottom: 40px;
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border-left: 5px solid #4facfe;
            scroll-margin-top: 120px;
        }
        
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e1e8ed;
        }
        
        .section-subtitle {
            color: #4facfe;
            margin: 25px 0 15px 0;
            font-size: 1.3em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-subtitle::before {
            content: "▶";
            font-size: 0.8em;
        }
        
        /* 问题样式 */
        .question {
            margin-bottom: 25px;
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }
        
        .question:hover {
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }
        
        .question-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 15px;
        }
        
        .question-number {
            background: #4facfe;
            color: white;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9em;
            font-weight: bold;
            flex-shrink: 0;
            transition: transform 0.2s ease;
        }
        
        .question:hover .question-number {
            transform: scale(1.1);
        }
        
        .question-text {
            font-size: 1.1em;
            font-weight: 600;
            color: #2c3e50;
            flex: 1;
            line-height: 1.5;
        }

        .answer-reference {
            font-size: 0.9em;
            color: #666;
            background: linear-gradient(135deg, #f8fbff 0%, #f0f7ff 100%);
            border-radius: 8px;
            margin-bottom: 12px;
            border-left: 3px solid #4facfe;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .answer-reference-header {
            padding: 10px 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(79, 172, 254, 0.05);
            border-bottom: 1px solid rgba(79, 172, 254, 0.1);
        }

        .answer-reference-header:hover {
            background: rgba(79, 172, 254, 0.08);
        }

        .answer-reference-title {
            font-weight: 600;
            color: #4facfe;
            font-size: 0.85em;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .answer-reference-toggle {
            font-size: 0.8em;
            color: #999;
            transition: transform 0.3s ease;
        }

        .answer-reference.expanded .answer-reference-toggle {
            transform: rotate(180deg);
        }

        .answer-reference-content {
            padding: 0 15px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .answer-reference.expanded .answer-reference-content {
            max-height: 1000px;
            padding: 12px 15px;
        }

        .answer-reference strong {
            color: #2c3e50;
            display: block;
            margin-bottom: 6px;
        }

        .input-group {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }

        .answer-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1em;
            min-height: 100px;
            resize: vertical;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .answer-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 4px rgba(79, 172, 254, 0.1);
        }

        .score-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
            min-width: 80px;
        }

        .score-input {
            width: 70px;
            height: 50px;
            padding: 8px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            text-align: center;
            font-size: 1.2em;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .score-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
            transform: scale(1.05);
        }

        .score-input.invalid {
            border-color: #ff6b6b;
            background-color: #ffe0e0;
        }

        .score-label {
            font-size: 0.85em;
            color: #666;
            text-align: center;
            font-weight: 600;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .input-group {
                flex-direction: column;
                gap: 15px;
            }

            .score-group {
                flex-direction: row;
                justify-content: center;
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>校招 - 杨柯 - 2025.08.12</h1>
            <div class="interview-info">
                <div><strong>👨‍💼 面试官：</strong>左浩</div>
                <div><strong>⏱️ 面试时长：</strong>60分钟</div>
                <div><strong>💻 面试方式：</strong>远程视频面试</div>
            </div>
        </div>
        
        <div class="content">
            <div class="navigation">
                <div class="nav-sections">
                    <a href="#section-opening" class="nav-btn">开场&自我介绍</a>
                    <a href="#section-frontend" class="nav-btn">基础题</a>
                    <a href="#section-vue" class="nav-btn">深度题</a>
                    <a href="#section-project" class="nav-btn">项目题</a>
                    <a href="#section-engineering" class="nav-btn">开放题</a>
                    <a href="#section-qa" class="nav-btn">问答环节</a>
                </div>
                <div class="controls-row">
                    <div class="candidate-info">
                        <label>候选人姓名：</label>
                        <input type="text" id="candidateName" class="candidate-input" placeholder="杨柯" required>
                        <label>面试日期：</label>
                        <input type="date" id="interviewDate" class="candidate-input" required>
                    </div>
                    <div class="score-display">
                        平均分：<span id="averageScore">0.0</span>/5.0
                    </div>
                    <div class="action-buttons">
                        <button onclick="autoSave()" class="btn btn-secondary">💾 保存进度</button>
                        <button onclick="exportToExcel()" class="btn btn-primary">📊 导出Excel</button>
                    </div>
                </div>
                <div class="progress-container">
                    <div class="progress-info">
                        <span>完成进度</span>
                        <span id="progressText">0/17 题</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>
            </div>

            <!-- 开场 & 自我介绍 -->
            <div class="section" id="section-opening">
                <h2 class="section-title">🎯 开场 & 自我介绍（5分钟）</h2>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">1</div>
                        <div class="question-text">面试官开场白（1分钟）</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考内容
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考内容：</strong>你好，同学！很高兴见到你，我是今天的面试官左浩。谢谢你抽时间来聊，咱们今天就轻松一点，当作一次技术交流。准备好了就开始吧。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人反应和回应..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">2</div>
                        <div class="question-text">候选人自我介绍（4分钟）- 请先做个自我介绍</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                📝 评分要点
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>评分要点：</strong>表达逻辑清晰、技术背景介绍、项目经验相关性、个人特点突出
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的自我介绍内容..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 💻 一、基础题（15分钟） -->
            <div class="section" id="section-frontend">
                <h2 class="section-title">💻 一、基础题（15分钟）</h2>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">3</div>
                        <div class="question-text">在 JavaScript 中，我们声明变量时常见的有哪些方式？能跟我说说 `var`、`let` 和 `const` 的区别，以及你在什么场景下会用它们吗？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>解析：</strong><br>
                            <strong>（少数人可能会提到 function、class、import 这种声明绑定的方式，但核心是前三个）</strong>
                            • `var`：函数作用域、变量提升（初始化为 `undefined`）、可重复声明。<br>
                            • `let`：块级作用域、不会变量提升（暂时性死区）、不可重复声明。<br>
                            • `const`：块级作用域、不可重复声明、声明时必须初始化，引用类型值可变但地址不可变。<br><br>
                            <strong>使用场景：</strong><br>
                            • `var`：旧项目兼容。<br>
                            • `let`：变量会变化时使用。<br>
                            • `const`：常量或不会变化的引用类型。<br><br>
                            <strong>延伸提问 & 解析：</strong><br>
                            1. **为什么 `const` 声明的对象属性仍然可以修改？**<br>
                            因为 `const` 只是锁定变量绑定的引用地址，对象内容（属性）并不受限制。<br><br>
                            2. **解释"暂时性死区"是怎么出现的？**<br>
                            在 `let`/`const` 声明之前访问该变量会报错，因为在声明之前处于暂时性死区（TDZ），该阶段变量已被绑定但未初始化。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">4</div>
                        <div class="question-text">JavaScript 的原型链是怎么工作的？能简单说一下访问属性时的查找过程吗？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>解析：</strong><br>
                            • 每个对象都有 `__proto__` 指向其构造函数的 `prototype`。<br>
                            • 访问属性时，先查自身属性，再沿原型链向上查找，直到 `Object.prototype` 或 `null`。<br>
                            • 继承方式：ES5（构造函数 + 原型链）、ES6（`class extends`）。<br><br>
                            <strong>延伸提问 & 解析：</strong><br>
                            1. **如何实现一个 `Object.create()`？**<br>
                            ```js<br>
                            function create(proto) {<br>
                              function F() {}<br>
                              F.prototype = proto;<br>
                              return new F();<br>
                            }<br>
                            ```<br><br>
                            2. **ES6 `class` 本质上和 ES5 继承有什么区别？**<br>
                            ES6 `class` 是语法糖，本质仍然基于原型链；但 `class` 的继承机制通过 `super` 调用父类构造函数，并且更直观。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">5</div>
                        <div class="question-text">你能简单描述一下浏览器事件循环（Event Loop）的执行顺序吗？微任务和宏任务的区别是什么？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>解析：</strong><br>
                            • 宏任务（macrotask）：`setTimeout`、`setInterval`、I/O。<br>
                            • 微任务（microtask）：`Promise.then`、`MutationObserver`、`queueMicrotask`。<br>
                            • 执行顺序：执行一个宏任务 → 执行所有微任务 → 渲染 → 下一个宏任务。<br><br>
                            <strong>延伸提问 & 解析：</strong><br>
                            1. **`async/await` 在事件循环中是怎么执行的？**<br>
                            `await` 会将后续代码放入微任务队列，等当前宏任务执行完后立即执行。<br><br>
                            2. **为什么在 `Promise` 中 `setTimeout` 会延迟执行？**<br>
                            因为 `Promise.then` 属于微任务，优先于宏任务的 `setTimeout`。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">6</div>
                        <div class="question-text">Flex 布局和 Grid 布局有什么区别？分别适合什么场景？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>解析：</strong><br>
                            • Flex：一维布局（主轴 + 交叉轴），适合组件级布局。<br>
                            • Grid：二维布局（行 + 列），适合整体页面布局。<br>
                            • Flex 常用属性：`justify-content`、`align-items`、`flex-grow`。<br>
                            • Grid 常用属性：`grid-template-rows`、`grid-template-columns`、`gap`。<br><br>
                            <strong>延伸提问 & 解析：</strong><br>
                            1. **如何用 Grid 实现一个 12 列栅格系统？**<br>
                            ```css<br>
                            display: grid;<br>
                            grid-template-columns: repeat(12, 1fr);<br>
                            gap: 10px;<br>
                            ```<br><br>
                            2. **Flex 布局中 `flex: 1` 代表什么？**<br>
                            表示 `flex-grow: 1`（可伸展），`flex-shrink: 1`（可收缩），`flex-basis: 0`（初始大小）。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">7</div>
                        <div class="question-text">HTTP/1.1 和 HTTP/2 在性能上的主要区别有哪些？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>解析：</strong><br>
                            • HTTP/1.1：基于文本、队头阻塞、多连接。<br>
                            • HTTP/2：二进制分帧、多路复用、头部压缩（HPACK）、服务器推送。<br>
                            • 提升原因：减少 TCP 连接数、降低延迟。<br><br>
                            <strong>延伸提问 & 解析：</strong><br>
                            1. **为什么 HTTP/2 可以多路复用？**<br>
                            通过二进制分帧将多个请求拆分为帧交错发送，共用一个 TCP 连接。<br><br>
                            2. **HTTP/3 又有什么改进？**<br>
                            基于 QUIC（UDP），解决 TCP 队头阻塞问题，支持 0-RTT 建连。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- ⚛️ 二、深度题（20分钟） -->
            <div class="section" id="section-vue">
                <h2 class="section-title">⚛️ 二、深度题（20分钟）</h2>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">8</div>
                        <div class="question-text">在 React 项目中，你有哪些优化渲染性能的经验？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>解析：</strong><br>
                            • `React.memo` 避免不必要渲染。<br>
                            • `useMemo`、`useCallback` 缓存计算与函数。<br>
                            • 虚拟列表。<br>
                            • 合理使用 `key`。<br>
                            • 分片渲染（`requestIdleCallback`）。<br>
                            • 避免 render 中做复杂计算。<br><br>
                            <strong>延伸提问 & 解析：</strong><br>
                            1. **`useCallback` 为什么有时会导致性能更差？**<br>
                            维护闭包依赖数组也有开销，如果函数调用频率低，缓存反而浪费性能。<br><br>
                            2. **React 18 自动批处理是如何优化的？**<br>
                            将多次状态更新合并为一次渲染，减少重绘次数。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">9</div>
                        <div class="question-text">你在简历里提到从 CRA 迁移到 Vite，能说说性能提升的原因吗？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>解析：</strong><br>
                            • Vite 基于 ESM 原生支持，启动快。<br>
                            • 按需编译，未访问模块不构建。<br>
                            • ESBuild 依赖预构建快。<br>
                            • HMR 更高效。<br><br>
                            <strong>延伸提问 & 解析：</strong><br>
                            1. **Vite 的 HMR 和 Webpack 的有什么区别？**<br>
                            Vite 直接替换模块文件，Webpack 需重新打包依赖链。<br><br>
                            2. **为什么 Vite 构建体积更小？**<br>
                            默认使用现代构建工具（Rollup + Tree Shaking）。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">10</div>
                        <div class="question-text">如果 ECharts 需要渲染 10 万个数据点，你会如何优化？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>解析：</strong><br>
                            • 数据抽样。<br>
                            • 分片渲染。<br>
                            • 离屏 Canvas。<br>
                            • WebGL 渲染。<br>
                            • 节流更新。<br><br>
                            <strong>延伸提问 & 解析：</strong><br>
                            1. **为什么 WebGL 在大数据渲染中更高效？**<br>
                            GPU 并行计算能力强，减少 CPU 绘制开销。<br><br>
                            2. **如何处理高频更新的内存占用？**<br>
                            使用对象池，减少 GC 压力。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">11</div>
                        <div class="question-text">你在和 iOS/Android 联调时遇到过哪些问题？是怎么解决的？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>解析：</strong><br>
                            • 数据结构不一致：统一协议。<br>
                            • 编码问题：统一 UTF-8。<br>
                            • 网络差异：弱网优化。<br>
                            • 平台差异：时间戳、浮点精度。<br><br>
                            <strong>延伸提问 & 解析：</strong><br>
                            1. **如何在前端保障数据一致性？**<br>
                            使用版本号/时间戳对比，确保最新数据覆盖旧数据。<br><br>
                            2. **移动端和 H5 调用同一接口的兼容性问题？**<br>
                            User-Agent 差异、接口缓存策略不同。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 💼 三、项目题（20分钟） -->
            <div class="section" id="section-project">
                <h2 class="section-title">💼 三、项目题（20分钟）</h2>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">12</div>
                        <div class="question-text">在智能设备管理项目中，你的缓存机制是怎么设计的？为什么能在高频刷新场景下提升性能？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>解析：</strong><br>
                            • 多层缓存。<br>
                            • 数据版本号判断。<br>
                            • Diff 更新。<br><br>
                            <strong>延伸提问 & 解析：</strong><br>
                            1. **你是如何判断数据变化的？**<br>
                            使用哈希/版本号比对。<br><br>
                            2. **WebSocket 断开时缓存如何保证一致性？**<br>
                            断线后全量拉取最新数据。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">13</div>
                        <div class="question-text">你提到在拓扑图中实现了自定义渲染引擎和 Diff 算法，它和 React 的 Diff 有什么不同？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>解析：</strong><br>
                            • React 针对 DOM。<br>
                            • Canvas 需自己维护虚拟节点状态。<br>
                            • 批量绘制减少 FPS 损耗。<br><br>
                            <strong>延伸提问 & 解析：</strong><br>
                            1. **如何调试性能瓶颈？**<br>
                            使用 Chrome Performance / FPS Meter。<br><br>
                            2. **Diff 算法复杂度？**<br>
                            O(n) 最优，O(n²) 需优化。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">14</div>
                        <div class="question-text">你是如何保证 WebSocket 数据更新延迟低于 200ms 的？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>解析：</strong><br>
                            • 心跳检测。<br>
                            • 数据压缩。<br>
                            • 就近服务器。<br>
                            • 增量同步。<br><br>
                            <strong>延伸提问 & 解析：</strong><br>
                            1. **WebSocket 如何处理丢包？**<br>
                            应用层补发/重试机制。<br><br>
                            2. **推送快于处理速度会怎样？**<br>
                            消息堆积，需限流或批处理。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">15</div>
                        <div class="question-text">你是如何设计高复用组件的？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>解析：</strong><br>
                            • 抽象逻辑。<br>
                            • 低耦合。<br>
                            • 可扩展性。<br><br>
                            <strong>延伸提问 & 解析：</strong><br>
                            1. **组件复用 vs 组件库建设？**<br>
                            组件复用偏内部项目，组件库需版本管理、文档、发布。<br><br>
                            2. **如何不破坏原有组件扩展功能？**<br>
                            使用高阶组件 / 组合模式。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- 🔧 四、开放题（5分钟） -->
            <div class="section" id="section-engineering">
                <h2 class="section-title">🔧 四、开放题（5分钟）</h2>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">16</div>
                        <div class="question-text">如果让你从零设计一个前端可视化实时监控系统，你会怎么考虑技术选型和性能优化？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>解析：</strong><br>
                            • 技术栈：React/Vue + ECharts/WebGL + WebSocket。<br>
                            • 数据流：Redux / Zustand / Recoil。<br>
                            • 性能：虚拟化、分片渲染、Web Worker。<br>
                            • 容灾：断线重连、缓存恢复。<br><br>
                            <strong>延伸提问 & 解析：</strong><br>
                            1. **百万级数据点可视化选型？**<br>
                            WebGL / GPU 加速方案。<br><br>
                            2. **低性能设备流畅策略？**<br>
                            数据降采样、动画降级。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- ❓ 五、问答环节（2分钟） -->
            <div class="section" id="section-qa">
                <h2 class="section-title">❓ 五、问答环节（2分钟）</h2>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">17</div>
                        <div class="question-text">同学，你有什么想问我的吗？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 考察重点
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>考察重点：</strong><br>
                            • 对公司和岗位的关注程度<br>
                            • 职业规划和学习态度<br>
                            • 沟通能力<br><br>
                            <strong>好的问题示例：</strong><br>
                            • 团队技术栈和发展方向<br>
                            • 项目的技术挑战<br>
                            • 团队协作方式<br>
                            • 个人成长机会
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的问题和表现..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 自动保存功能
        function autoSave() {
            const data = {
                candidateName: document.getElementById('candidateName').value,
                interviewDate: document.getElementById('interviewDate').value,
                answers: [],
                scores: []
            };

            // 收集所有答案和分数
            const answerInputs = document.querySelectorAll('.answer-input');
            const scoreInputs = document.querySelectorAll('.score-input');

            answerInputs.forEach((input, index) => {
                data.answers[index] = input.value;
            });

            scoreInputs.forEach((input, index) => {
                data.scores[index] = input.value;
            });

            // 保存到localStorage
            localStorage.setItem('interview_data_liangqikun', JSON.stringify(data));
            console.log('数据已保存到本地！');
        }

        // 加载保存的数据
        function loadSavedData() {
            const savedData = localStorage.getItem('interview_data_yangke');
            if (savedData) {
                const data = JSON.parse(savedData);

                if (data.candidateName) {
                    document.getElementById('candidateName').value = data.candidateName;
                }
                if (data.interviewDate) {
                    document.getElementById('interviewDate').value = data.interviewDate;
                }

                // 恢复答案
                const answerInputs = document.querySelectorAll('.answer-input');
                answerInputs.forEach((input, index) => {
                    if (data.answers && data.answers[index]) {
                        input.value = data.answers[index];
                    }
                });

                // 恢复分数
                const scoreInputs = document.querySelectorAll('.score-input');
                scoreInputs.forEach((input, index) => {
                    if (data.scores && data.scores[index]) {
                        input.value = data.scores[index];
                    }
                });

                calculateAverage();
            }
        }

        // 计算平均分
        function calculateAverage() {
            const scoreInputs = document.querySelectorAll('.score-input');
            let total = 0;
            let count = 0;

            scoreInputs.forEach(input => {
                const value = parseFloat(input.value);
                if (!isNaN(value) && value > 0) {
                    total += value;
                    count++;
                }
            });

            const average = count > 0 ? (total / count).toFixed(1) : '0.0';
            document.getElementById('averageScore').textContent = average;

            // 更新进度
            const totalQuestions = scoreInputs.length;
            const completedQuestions = count;
            const progressPercent = (completedQuestions / totalQuestions) * 100;

            document.getElementById('progressText').textContent = `${completedQuestions}/${totalQuestions} 题`;
            document.getElementById('progressFill').style.width = `${progressPercent}%`;
        }

        // 验证分数输入
        function validateScore(input) {
            const value = parseFloat(input.value);
            if (isNaN(value) || value < 1 || value > 5) {
                input.classList.add('invalid');
            } else {
                input.classList.remove('invalid');
            }
        }

        // 切换参考答案显示
        function toggleReference(header) {
            const reference = header.parentElement;
            reference.classList.toggle('expanded');
        }

        // 导出Excel功能
        function exportToExcel() {
            const candidateName = document.getElementById('candidateName').value || '梁琦坤';
            const interviewDate = document.getElementById('interviewDate').value || new Date().toISOString().split('T')[0];

            // 收集数据
            const data = [];
            const questions = document.querySelectorAll('.question');

            questions.forEach((question, index) => {
                const questionText = question.querySelector('.question-text').textContent;
                const answer = question.querySelector('.answer-input').value;
                const score = question.querySelector('.score-input').value;

                data.push({
                    '题号': index + 1,
                    '问题': questionText,
                    '候选人回答': answer,
                    '分数': score
                });
            });

            // 创建工作簿
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(data);

            // 设置列宽
            ws['!cols'] = [
                { width: 8 },   // 题号
                { width: 50 },  // 问题
                { width: 60 },  // 回答
                { width: 10 }   // 分数
            ];

            XLSX.utils.book_append_sheet(wb, ws, '面试记录');

            // 导出文件
            const fileName = `面试记录_${candidateName}_${interviewDate}.xlsx`;
            XLSX.writeFile(wb, fileName);
        }

        // 导航功能
        function initNavigation() {
            const navBtns = document.querySelectorAll('.nav-btn');
            const sections = document.querySelectorAll('.section');

            // 点击导航按钮
            navBtns.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    const targetId = btn.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(targetId);

                    if (targetSection) {
                        targetSection.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });

            // 滚动时更新导航状态
            window.addEventListener('scroll', () => {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop - 150;
                    if (window.pageYOffset >= sectionTop) {
                        current = section.getAttribute('id');
                    }
                });

                navBtns.forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.getAttribute('href') === `#${current}`) {
                        btn.classList.add('active');
                    }
                });
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSavedData();
            initNavigation();

            // 设置默认日期
            const today = new Date().toISOString().split('T')[0];
            if (!document.getElementById('interviewDate').value) {
                document.getElementById('interviewDate').value = today;
            }

            // 自动保存
            setInterval(autoSave, 30000); // 每30秒自动保存一次
        });
    </script>
</body>
</html>
