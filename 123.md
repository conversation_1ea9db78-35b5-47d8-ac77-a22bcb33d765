### **一、基础题（15 分钟）**

---

#### **1. 题目：**

> 我们先从 JavaScript 基础开始，能跟我说说 `var`、`let` 和 `const` 的区别，以及你在什么场景下会用它们吗？

**解析：**

* `var`：函数作用域、变量提升（初始化为 `undefined`）、可重复声明。
* `let`：块级作用域、不会变量提升（暂时性死区）、不可重复声明。
* `const`：块级作用域、不可重复声明、声明时必须初始化，引用类型值可变但地址不可变。
  **使用场景：**
* `var`：旧项目兼容。
* `let`：变量会变化时使用。
* `const`：常量或不会变化的引用类型。

**延伸提问 & 解析：**

1. **为什么 `const` 声明的对象属性仍然可以修改？**
   因为 `const` 只是锁定变量绑定的引用地址，对象内容（属性）并不受限制。
2. **解释“暂时性死区”是怎么出现的？**
   在 `let`/`const` 声明之前访问该变量会报错，因为在声明之前处于暂时性死区（TDZ），该阶段变量已被绑定但未初始化。

---

#### **2. 题目：**

> JavaScript 的原型链是怎么工作的？能简单画一下访问属性时的查找过程吗？

**解析：**

* 每个对象都有 `__proto__` 指向其构造函数的 `prototype`。
* 访问属性时，先查自身属性，再沿原型链向上查找，直到 `Object.prototype` 或 `null`。
* 继承方式：ES5（构造函数 + 原型链）、ES6（`class extends`）。

**延伸提问 & 解析：**

1. **如何实现一个 `Object.create()`？**

   ```js
   function create(proto) {
     function F() {}
     F.prototype = proto;
     return new F();
   }
   ```
2. **ES6 `class` 本质上和 ES5 继承有什么区别？**
   ES6 `class` 是语法糖，本质仍然基于原型链；但 `class` 的继承机制通过 `super` 调用父类构造函数，并且更直观。

---

#### **3. 题目：**

> 你能简单描述一下浏览器事件循环（Event Loop）的执行顺序吗？微任务和宏任务的区别是什么？

**解析：**

* 宏任务（macrotask）：`setTimeout`、`setInterval`、I/O。
* 微任务（microtask）：`Promise.then`、`MutationObserver`、`queueMicrotask`。
* 执行顺序：执行一个宏任务 → 执行所有微任务 → 渲染 → 下一个宏任务。

**延伸提问 & 解析：**

1. **`async/await` 在事件循环中是怎么执行的？**
   `await` 会将后续代码放入微任务队列，等当前宏任务执行完后立即执行。
2. **为什么在 `Promise` 中 `setTimeout` 会延迟执行？**
   因为 `Promise.then` 属于微任务，优先于宏任务的 `setTimeout`。

---

#### **4. 题目：**

> Flex 布局和 Grid 布局有什么区别？分别适合什么场景？

**解析：**

* Flex：一维布局（主轴 + 交叉轴），适合组件级布局。
* Grid：二维布局（行 + 列），适合整体页面布局。
* Flex 常用属性：`justify-content`、`align-items`、`flex-grow`。
* Grid 常用属性：`grid-template-rows`、`grid-template-columns`、`gap`。

**延伸提问 & 解析：**

1. **如何用 Grid 实现一个 12 列栅格系统？**

   ```css
   display: grid;
   grid-template-columns: repeat(12, 1fr);
   gap: 10px;
   ```
2. **Flex 布局中 `flex: 1` 代表什么？**
   表示 `flex-grow: 1`（可伸展），`flex-shrink: 1`（可收缩），`flex-basis: 0`（初始大小）。

---

#### **5. 题目：**

> HTTP/1.1 和 HTTP/2 在性能上的主要区别有哪些？

**解析：**

* HTTP/1.1：基于文本、队头阻塞、多连接。
* HTTP/2：二进制分帧、多路复用、头部压缩（HPACK）、服务器推送。
* 提升原因：减少 TCP 连接数、降低延迟。

**延伸提问 & 解析：**

1. **为什么 HTTP/2 可以多路复用？**
   通过二进制分帧将多个请求拆分为帧交错发送，共用一个 TCP 连接。
2. **HTTP/3 又有什么改进？**
   基于 QUIC（UDP），解决 TCP 队头阻塞问题，支持 0-RTT 建连。

---

### **二、深度题（20 分钟）**

---

#### **6. 题目：**

> 在 React 项目中，你有哪些优化渲染性能的经验？

**解析：**

* `React.memo` 避免不必要渲染。
* `useMemo`、`useCallback` 缓存计算与函数。
* 虚拟列表。
* 合理使用 `key`。
* 分片渲染（`requestIdleCallback`）。
* 避免 render 中做复杂计算。

**延伸提问 & 解析：**

1. **`useCallback` 为什么有时会导致性能更差？**
   维护闭包依赖数组也有开销，如果函数调用频率低，缓存反而浪费性能。
2. **React 18 自动批处理是如何优化的？**
   将多次状态更新合并为一次渲染，减少重绘次数。

---

#### **7. 题目：**

> 你在简历里提到从 CRA 迁移到 Vite，能说说性能提升的原因吗？

**解析：**

* Vite 基于 ESM 原生支持，启动快。
* 按需编译，未访问模块不构建。
* ESBuild 依赖预构建快。
* HMR 更高效。

**延伸提问 & 解析：**

1. **Vite 的 HMR 和 Webpack 的有什么区别？**
   Vite 直接替换模块文件，Webpack 需重新打包依赖链。
2. **为什么 Vite 构建体积更小？**
   默认使用现代构建工具（Rollup + Tree Shaking）。

---

#### **8. 题目：**

> 如果 ECharts 需要渲染 10 万个数据点，你会如何优化？

**解析：**

* 数据抽样。
* 分片渲染。
* 离屏 Canvas。
* WebGL 渲染。
* 节流更新。

**延伸提问 & 解析：**

1. **为什么 WebGL 在大数据渲染中更高效？**
   GPU 并行计算能力强，减少 CPU 绘制开销。
2. **如何处理高频更新的内存占用？**
   使用对象池，减少 GC 压力。

---

#### **9. 题目：**

> 你在和 iOS/Android 联调时遇到过哪些问题？是怎么解决的？

**解析：**

* 数据结构不一致：统一协议。
* 编码问题：统一 UTF-8。
* 网络差异：弱网优化。
* 平台差异：时间戳、浮点精度。

**延伸提问 & 解析：**

1. **如何在前端保障数据一致性？**
   使用版本号/时间戳对比，确保最新数据覆盖旧数据。
2. **移动端和 H5 调用同一接口的兼容性问题？**
   User-Agent 差异、接口缓存策略不同。

---

### **三、项目题（20 分钟）**

---

#### **10. 题目：**

> 在智能设备管理项目中，你的缓存机制是怎么设计的？为什么能在高频刷新场景下提升性能？

**解析：**

* 多层缓存。
* 数据版本号判断。
* Diff 更新。

**延伸提问 & 解析：**

1. **你是如何判断数据变化的？**
   使用哈希/版本号比对。
2. **WebSocket 断开时缓存如何保证一致性？**
   断线后全量拉取最新数据。

---

#### **11. 题目：**

> 你提到在拓扑图中实现了自定义渲染引擎和 Diff 算法，它和 React 的 Diff 有什么不同？

**解析：**

* React 针对 DOM。
* Canvas 需自己维护虚拟节点状态。
* 批量绘制减少 FPS 损耗。

**延伸提问 & 解析：**

1. **如何调试性能瓶颈？**
   使用 Chrome Performance / FPS Meter。
2. **Diff 算法复杂度？**
   O(n) 最优，O(n²) 需优化。

---

#### **12. 题目：**

> 你是如何保证 WebSocket 数据更新延迟低于 200ms 的？

**解析：**

* 心跳检测。
* 数据压缩。
* 就近服务器。
* 增量同步。

**延伸提问 & 解析：**

1. **WebSocket 如何处理丢包？**
   应用层补发/重试机制。
2. **推送快于处理速度会怎样？**
   消息堆积，需限流或批处理。

---

#### **13. 题目：**

> 你是如何设计高复用组件的？

**解析：**

* 抽象逻辑。
* 低耦合。
* 可扩展性。

**延伸提问 & 解析：**

1. **组件复用 vs 组件库建设？**
   组件复用偏内部项目，组件库需版本管理、文档、发布。
2. **如何不破坏原有组件扩展功能？**
   使用高阶组件 / 组合模式。

---

### **四、开放题（5 分钟）**

---

#### **14. 题目：**

> 如果让你从零设计一个前端可视化实时监控系统，你会怎么考虑技术选型和性能优化？

**解析：**

* 技术栈：React/Vue + ECharts/WebGL + WebSocket。
* 数据流：Redux / Zustand / Recoil。
* 性能：虚拟化、分片渲染、Web Worker。
* 容灾：断线重连、缓存恢复。

**延伸提问 & 解析：**

1. **百万级数据点可视化选型？**
   WebGL / GPU 加速方案。
2. **低性能设备流畅策略？**
   数据降采样、动画降级。

---